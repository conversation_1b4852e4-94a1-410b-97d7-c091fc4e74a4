import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "../components/themeProvider";
import { FullNavbar } from "@/components/ui/full-navbar";
import { MiniNavbar } from "@/components/ui/mini-navbar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "John <PERSON>e - Full-Stack Developer & UI/UX Designer",
  description: "Portfolio of John <PERSON> - Full-Stack Developer specializing in React, Next.js, and modern web technologies. Creating beautiful, functional, and user-centered digital experiences.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <FullNavbar />
          <MiniNavbar />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
